import React, { useState } from "react";
import { Button, Upload, Alert, App, Spin, Tag, Avatar } from "antd";
import {
  UploadOutlined,
  InboxOutlined,
  DeleteOutlined,
  FileOutlined,
} from "@ant-design/icons";
import type { UploadProps, UploadFile } from "antd";
import type { DocumentUploadFormData } from "@/types/applicationForm";
import { UploadStatus } from "@/types/applicationForm";
import styles from "./DocumentUploadStep.module.less";

const { Dragger } = Upload;

interface DocumentUploadStepProps {
  onNext: (data: DocumentUploadFormData) => void;
  onBack: () => void;
  initialData?: DocumentUploadFormData;
}

interface FileCard {
  id: string;
  name: string;
  status: "uploading" | "completed" | "error";
  file?: File;
  url?: string;
  errorMessage?: string;
}

const DocumentUploadStep: React.FC<DocumentUploadStepProps> = ({
  onNext,
  onBack,
  initialData,
}) => {
  const { message } = App.useApp();
  const [fileCards, setFileCards] = useState<FileCard[]>([]);
  const [uploading, setUploading] = useState(false);

  // 模拟文件上传
  const uploadFileAsync = async (fileCard: FileCard): Promise<void> => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟随机成功/失败
        if (Math.random() > 0.2) {
          resolve();
        } else {
          reject(new Error("Upload failed"));
        }
      }, 2000 + Math.random() * 3000); // 2-5秒随机延迟
    });
  };

  // 处理文件选择
  const handleFileSelect = async (fileList: File[]) => {
    const newCards: FileCard[] = fileList.map((file) => ({
      id: `${Date.now()}-${Math.random()}`,
      name: file.name,
      status: "uploading",
      file,
    }));

    // 立即显示卡片
    setFileCards((prev) => [...prev, ...newCards]);
    // 逐个上传文件
    for (const card of newCards) {
      try {
        await uploadFileAsync(card);
        // 上传成功
        setFileCards((prev) =>
          prev.map((c) =>
            c.id === card.id
              ? { ...c, status: "completed", url: `/uploads/${card.name}` }
              : c
          )
        );
      } catch (error) {
        // 上传失败
        setFileCards((prev) =>
          prev.map((c) =>
            c.id === card.id
              ? { ...c, status: "error", errorMessage: "Upload failed" }
              : c
          )
        );
      }
    }
  };

  const uploadProps: UploadProps = {
    name: "file",
    multiple: true,
    accept: ".png,.jpg,.jpeg,.pdf",
    showUploadList: false, // 隐藏默认的文件列表
    beforeUpload: (file, fileList) => {
      const isValidType = [
        "image/png",
        "image/jpeg",
        "application/pdf",
      ].includes(file.type);
      if (!isValidType) {
        message.error("只能上传 PNG、JPEG 或 PDF 格式的文件！");
        return false;
      }

      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error("文件大小不能超过 5MB！");
        return false;
      }

      // 处理所有选中的文件
      if (fileList) {
        handleFileSelect(fileList);
      }

      return false; // 阻止默认上传
    },
  };

  const handleNext = () => {
    if (fileCards.length === 0) {
      message.warning("请至少上传一个文档");
      return;
    }

    const uploadedFiles = fileCards.filter(
      (card) => card.status === "completed"
    );
    if (uploadedFiles.length !== fileCards.length) {
      message.warning("请等待所有文件上传完成");
      return;
    }

    const formData: DocumentUploadFormData = {
      files: fileCards.map((card) => ({
        id: card.id,
        name: card.name,
        url: card.url || "",
        status: UploadStatus.SUCCESS,
        size: card.file?.size || 0,
        type: card.file?.type || "",
      })),
    };

    onNext(formData);
  };

  // 删除文件卡片
  const handleDeleteCard = (cardId: string) => {
    setFileCards((prev) => prev.filter((card) => card.id !== cardId));
  };

  // 渲染文件卡片 - 复用ShareholderInfoStep的样式
  const renderFileCard = (card: FileCard) => {
    if (card.status === "uploading") {
      return (
        <div key={card.id} className={styles.shareholderCard}>
          <Button
            type="text"
            icon={<DeleteOutlined />}
            className={styles.deleteButton}
            onClick={() => handleDeleteCard(card.id)}
          />
          <div className={styles.processingContent}>
            <Spin size="large" />
            <div className={styles.processingText}>
              Uploading {card.name}...
            </div>
          </div>
        </div>
      );
    }

    if (card.status === "error") {
      return (
        <div key={card.id} className={styles.shareholderCard}>
          <Button
            type="text"
            icon={<DeleteOutlined />}
            className={styles.deleteButton}
            onClick={() => handleDeleteCard(card.id)}
          />
          <div className={styles.errorContent}>
            <div className={styles.fileName}>{card.name}</div>
            <div className={styles.errorMessage}>
              {card.errorMessage || "Upload failed"}
            </div>
          </div>
        </div>
      );
    }

    // Completed status - 使用ShareholderInfoStep的完整卡片样式
    return (
      <div key={card.id} className={styles.shareholderCard}>
        <Button
          type="text"
          icon={<DeleteOutlined />}
          className={styles.deleteButton}
          onClick={() => handleDeleteCard(card.id)}
        />

        <div className={styles.cardContent}>
          <div className={styles.avatarSection}>
            <Avatar
              size={72}
              icon={<FileOutlined />}
              className={styles.avatar}
            />
          </div>

          <div className={styles.tags}>
            <Tag color="green" className={styles.fileTag}>
              Document
            </Tag>
            <Tag color="blue" className={styles.statusTag}>
              Uploaded
            </Tag>
          </div>

          <div className={styles.infoSection}>
            <div className={styles.infoItem}>
              <div className={styles.label}>File Name</div>
              <div className={styles.value}>{card.name}</div>
            </div>
            <div className={styles.infoItem}>
              <div className={styles.label}>File Size</div>
              <div className={styles.value}>
                {card.file
                  ? `${(card.file.size / 1024 / 1024).toFixed(2)} MB`
                  : "Unknown"}
              </div>
            </div>
            <div className={styles.infoItem}>
              <div className={styles.label}>File Type</div>
              <div className={styles.value}>{card.file?.type || "Unknown"}</div>
            </div>
            <div className={styles.infoItem}>
              <div className={styles.label}>Upload Status</div>
              <div className={styles.value}>Completed</div>
            </div>
          </div>
        </div>

        <div className={styles.buttonSection}>
          <div className={styles.completionText}>Upload 100%</div>
          <Button
            type="default"
            size="large"
            onClick={() => {
              if (card.url) {
                window.open(card.url, "_blank");
              }
            }}
            className={styles.actionButton}
          >
            View File
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className={styles.documentUploadStep}>
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.header}>
            <h1 className={styles.title}>
              Kindly upload the documents of all directors and shareholders of
              the company.
            </h1>
          </div>

          <div className={styles.instructions}>
            <Alert
              message="For Individual Directors and Shareholders:"
              description={
                <div className={styles.instructionList}>
                  <div className={styles.instructionItem}>
                    <span className={styles.bold}>Malaysian</span>: Upload a
                    copy of your Malaysian identity card (MyKad).
                  </div>
                  <div className={styles.instructionItem}>
                    <span className={styles.bold}>Foreigners</span>: Upload a
                    valid passport with a clear photo and signature.
                  </div>
                </div>
              }
              type="info"
              showIcon={false}
              className={styles.instructionCard}
            />

            <Alert
              message="For Corporate Directors & Shareholders:"
              description="Upload a certified copy of the company's Certificate of Incorporation."
              type="info"
              showIcon={false}
              className={styles.instructionCard}
            />
          </div>

          <div className={styles.uploadSection}>
            <Dragger {...uploadProps} className={styles.uploadArea}>
              <div className={styles.uploadContent}>
                <div className={styles.uploadIcon}>
                  <InboxOutlined />
                </div>
                <div className={styles.uploadText}>
                  <div className={styles.uploadAction}>
                    <span className={styles.clickText}>Click to upload</span>
                    <span className={styles.dragText}>or drag and drop</span>
                  </div>
                  <div className={styles.supportText}>
                    PNG, JPEG, PDF (max size file limit 5MB)
                  </div>
                </div>
              </div>
            </Dragger>
          </div>
        </div>
      </div>
      <div className={styles.shareholderGridContainer}>
        {fileCards.length > 0 && (
          <>
            <h1 className={styles.shareholderGridTitle}>
              Complete details for all directors, shareholders, and ultimate
              beneficial owners (UBOs).
            </h1>
            <div className={styles.shareholderGrid}>
              {fileCards.map(renderFileCard)}
            </div>

            <div className={styles.buttonGroup}>
              <Button
                type="default"
                size="large"
                onClick={onBack}
                className={styles.backButton}
              >
                Back
              </Button>
              <Button
                type="primary"
                size="large"
                onClick={handleNext}
                loading={uploading}
                className={styles.nextButton}
              >
                Next
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default DocumentUploadStep;
